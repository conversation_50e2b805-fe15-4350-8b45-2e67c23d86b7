import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '事件Id',
    dataIndex: 'eventId',
    width: 150,
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    width: 150
  },
  {
    title: '事件详情',
    dataIndex: 'stripeObject',
    width: 300
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '关键词',
    field: 'keyword',
    component: 'Input',
    colProps: { span: 8 }
  },
]
