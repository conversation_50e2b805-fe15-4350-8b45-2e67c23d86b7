import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '三方平台',
    dataIndex: 'socialType',
    width: 60,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.AI_BASE_SOCIAL_TYPE)
    }
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: 'openid',
    dataIndex: 'openid',
    width: 140,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 160,
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    width: 120,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 180,
  },
  {
    title: '客户ID',
    dataIndex: 'stripeCustomerId',
    width: 140,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '用户状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.USER_STATUS_TYPE)
    }
  },
  {
    title: '是否是内部用户',
    dataIndex: 'isInternalUser',
    width: 120,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.Is_Internal_User)
    }
  },
  {
    title: '过期时间',
    dataIndex: 'expireAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD')
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 }
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '邮箱',
    field: 'email',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '用户名',
    field: 'userName',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '手机号',
    field: 'mobile',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '创建时间',
    field: 'createdAt',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
  {
    label: '过期时间',
    field: 'expireAt',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '三方用户ID',
    field: 'socialUserId',
    required: true,
    component: 'Input'
  },
  {
    label: 'openid',
    field: 'openid',
    required: true,
    component: 'Input'
  },
  {
    label: '用户名',
    field: 'userName',
    required: true,
    component: 'Input'
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'Input'
  },
  {
    label: '手机号',
    field: 'mobile',
    component: 'Input'
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input'
  },
  {
    label: '创建时间',
    field: 'createdAt',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    }
  },
  // {
  //   label: '用户状态',
  //   field: 'status',
  //   required: true,
  //   component: 'RadioButtonGroup',
  //   componentProps: {
  //       options: getDictOptions(DICT_TYPE.USER_STATUS_TYPE, 'number'),
  //   }
  // },
  {
    label: '过期时间',
    field: 'expireAt',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
    helpMessage: "过期时间计算到天"
  },
]

export const updateFormSchema: FormSchema[] = [
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '三方用户ID',
    field: 'socialUserId',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: 'openid',
    field: 'openid',
    dynamicDisabled: true,
    component: 'Input'
  },
  {
    label: '用户名',
    field: 'userName',
    required: true,
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '邮箱',
    field: 'email',
    required: true,
    component: 'Input',
    dynamicDisabled: (formValues) => {
    return formValues.model.socialType != 0
    }
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'Input'
  },
  {
    label: '手机号',
    field: 'mobile',
    required: true,
    component: 'Input'
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input'
  },
  {
    label: '创建时间',
    field: 'createdAt',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    }
  },
  // {
  //   label: '用户状态',
  //   field: 'status',
  //   required: true,
  //   component: 'RadioButtonGroup',
  //   componentProps: {
  //     options: getDictOptions(DICT_TYPE.USER_STATUS_TYPE, 'number'),
  //   }
  // },
  {
    label: '是否是内部用户',
    field: 'isInternalUser',
    required: false,
    component: 'RadioButtonGroup',
    componentProps: {
      options: getDictOptions(DICT_TYPE.Is_Internal_User, 'number'),
    }
  },
  {
    label: '过期时间',
    field: 'expireAt',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
    helpMessage: "过期时间计算到天,已过期的用户不能设置为启用"
  },
]
