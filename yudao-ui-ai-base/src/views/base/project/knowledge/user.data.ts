import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import {formatToDate} from '@/utils/dateUtil'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '三方平台',
    dataIndex: 'socialType',
    width: 60,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.AI_BASE_SOCIAL_TYPE)
    }
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 160,
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 160,
    customRender: ({ text }) => {
      return text === 1 ? '目录' : '文件'
    }
  },
  {
    title: '名称',
    dataIndex: 'fileName',
    width: 160,
  },
  {
    title: '父级ID',
    dataIndex: 'pid',
    width: 160,
  },
  {
    title: '层级',
    dataIndex: 'level',
    width: 160,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 },
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '名称',
    field: 'fileName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '父级ID',
    field: 'pid',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: [{'value': '1', 'label': '目录'}, {'value': '2', 'label': '文件'}],
    },
    colProps: { span: 8 },
  }
]
