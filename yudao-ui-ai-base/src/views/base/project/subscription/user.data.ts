import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 140,
  },
  {
    title: '支付状态',
    dataIndex: 'paymentStatus',
    width: 120,
  },
  {
    title: '应用Uuid',
    dataIndex: 'appUuid',
    width: 140,
  },
  {
    title: '订单Id',
    dataIndex: 'piId',
    width: 140,
  },
  {
    title: '订阅ID',
    dataIndex: 'subId',
    width: 140,
  },
  {
    title: '订单Id',
    dataIndex: 'piId',
    width: 140,
  },
  {
    title: '价格ID',
    dataIndex: 'priceId',
    width: 180,
  },
  {
    title: '订阅链接ID',
    dataIndex: 'checkoutSessionId',
    width: 160,
  },
  {
    title: '退订ID',
    dataIndex: 'unsubEventId',
    width: 140,
  },
  {
    title: '付款次数',
    dataIndex: 'paymentNum',
    width: 140,
  },
  {
    title: '支付链接',
    dataIndex: 'url',
    width: 250,
    // customRender: ({ text }) => {
    //   return text;
    // }
  },
  {
    title: '月数',
    dataIndex: 'monthNum',
    width: 80,
  },
  {
    title: '过期时间',
    dataIndex: 'expiredAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD')
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 },
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '应用Uuid',
    field: 'appUuid',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '订单id',
    field: 'piId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '订阅链接ID',
    field: 'checkoutSessionId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '订阅ID',
    field: 'subId',
    component: 'Input',
    colProps: { span: 8 }
  },
  // {
  //   label: '订单Id',
  //   field: 'piId',
  //   component: 'Input',
  //   colProps: { span: 8 }
  // },
  {
    label: '创建时间',
    field: 'createdAt',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 00:00:00',
    },
  },
]
