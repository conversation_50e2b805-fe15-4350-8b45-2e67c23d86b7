<script lang="ts" setup>
import { columns,searchFormSchema } from './user.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import {  subLogPage } from '@/api/common'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'
import { useRoute, useRouter } from 'vue-router'

defineOptions({ name: 'Subscription' })
const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

searchFormSchema[1].defaultValue = route.query.socialUserId
const [registerTable, { getForm, reload }] = useTable({
  title: '订阅链接日志列表',
  api: subLogPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
    actionColumn: {
    width: 120,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})
function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}
const handleLog = (record)=>{
  router.push(`/project/order/log?checkoutSessionId=${record.checkoutSessionId}`)
}
async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportUsers(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    }
  })
}

async function handleDelete(record: Recordable) {
  await deleteUsers(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
            { icon: IconEnum.LOG, label: '订单列表',  onClick: handleLog.bind(null, record) },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
