package cn.iocoder.yudao.aiBase.dto.request;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主站用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MedsciUsersPageReqVO extends PageParam {

    @Schema(description =  "三方ID")
    private String openid;

    /**
     * 枚举 {@link SocialTypeEnum}
     */
    @Schema(description =  "三方平台类型")
    private Integer socialType;


    @Schema(description = "主站用户ID", example = "4708")
    private Long socialUserId;

    @Schema(description = "用户名", example = "王五")
    private String userName;

    @Schema(description = "邮箱", example = "王五")
    private String email;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "真实姓名", example = "赵六")
    private String realName;

    @Schema(description = "关键词", example = "赵六")
    private String keyword;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdAt;

    @Schema(description = "1启用，2禁用", example = "2")
    private Integer status;

    @Schema(description = "过期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expireAt;

    @Schema(description = "过期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime expireAt1;

}